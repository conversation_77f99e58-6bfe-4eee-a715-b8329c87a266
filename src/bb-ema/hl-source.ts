import { Candle, CandleFeed, Exchange, OrderParams, Timeframe } from './bot';
import { infoClient, subsClient } from '../hyperliquid';
import { Candle as HLCandle } from '@nktkas/hyperliquid';
import { COIN } from './constants';

export class HLFeed implements CandleFeed {
  async subscribe(tf: Timeframe, cb: (c: Candle) => void) {
    const snapshot = await infoClient.candleSnapshot({
      coin: COIN,
      interval: tf,
      startTime: Date.now() - 1000 * 60 * 60 * 24 * 7,
    });
    snapshot.sort((a, b) => a.T - b.T);
    let current: HLCandle | null = null;
    for (const candle of snapshot) {
      if (candle.T > Date.now()) {
        current = candle;
        break;
      }
      cb({
        open: Number(candle.o),
        high: Number(candle.h),
        low: Number(candle.l),
        close: Number(candle.c),
        volume: Number(candle.v),
        ts: Number(candle.T),
        tf: tf,
      });
    }
    await subsClient.candle(
      {
        coin: COIN,
        interval: tf,
      },
      next => {
        if (current && current.T <= Date.now() && next.T > Date.now()) {
          cb({
            open: Number(current.o),
            high: Number(current.h),
            low: Number(current.l),
            close: Number(current.c),
            volume: Number(current.v),
            ts: Number(current.T),
            tf: tf,
          });
        }
        current = next;
      },
    );
  }
}

// Example: exchange wrapper
export class HLExchange implements Exchange {
  async postOrder(o: OrderParams) {
    // 1) open market/limit long
    // 2) attach TP1 / TP2 as reduce-only limits
    // 3) attach stop-loss
    // All via broker REST/WS API
  }
  async getPosition() {
    /* … */ return 0;
  }
  getEquity() {
    /* … */ return 5_000;
  }
}
