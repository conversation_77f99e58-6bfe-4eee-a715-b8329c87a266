import { CandleFeed, Exchange, TradeSleeveBot } from './bot';
import { HistoryProvider } from './history-provider';
import { HistoryExchange, HistoryFeed } from './history-source';
import { HLExchange, HLFeed } from './hl-source';
import { ASSET_ID, COIN, config } from './constants';
import { OrderParams } from '@nktkas/hyperliquid/esm/src/types/exchange/requests';
import { exchangeClient, infoClient } from '../hyperliquid';

interface Source {
  feed: CandleFeed;
  exchange: Exchange;
}

const history = new HistoryProvider();
const historySource: Source = {
  feed: new HistoryFeed(history),
  exchange: new HistoryExchange(history),
};

const liveSource: Source = {
  feed: new HLFeed(),
  exchange: new HLExchange(),
};

const TEST = true;
const source = TEST ? historySource : liveSource;

const bot = new TradeSleeveBot(config, source.feed, source.exchange);

async function main() {
  const data = await infoClient.allMids();
  const entryPrice = Number(data[COIN]);
  const response = await exchangeClient.order({
    orders: [
      {
        a: ASSET_ID,
        b: true,
        p: (entryPrice * 1.1).toFixed(3),
        s: '1',
        r: false,
        t: {
          limit: {
            tif: 'Gtc',
          },
        },
      },
      {
        a: ASSET_ID,
        b: false,
        p: (entryPrice * 0.98).toFixed(3),
        s: '1',
        r: true,
        t: {
          trigger: {
            isMarket: true,
            triggerPx: (entryPrice * 0.98).toFixed(3),
            tpsl: 'sl',
          },
        },
      },
      {
        a: ASSET_ID,
        b: false,
        p: (entryPrice * 1.02).toFixed(3),
        s: '0.5',
        r: true,
        t: {
          trigger: {
            isMarket: true,
            triggerPx: (entryPrice * 1.02).toFixed(3),
            tpsl: 'tp',
          },
        },
      },
    ],
    grouping: 'normalTpsl',
  });
}

//void main();

if (TEST) {
  history.positionManager.trackStart();
  history.play();
  history.positionManager.printReport();
}
