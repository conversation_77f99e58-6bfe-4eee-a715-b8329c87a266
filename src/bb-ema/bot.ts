/**********************************************************************
 * BB-EMA Trade-Sleeve <PERSON> (TypeScript) — v0.1
 * ---------------------------------------------------------------
 *  - Core: keep 75 % long while 1-h price > rising EMA-21
 *  - Trade sleeve (25 %):
 *      • Ride-the-rail: candle closes above BB-upper + 0.3 % vs EMA-9
 *      • Dip-buy: wick tags EMA-9 or BB-mid, candle closes back above
 *      • Stops / TP1 / TP2 = 1 R / +1 R / +2 R (calculated at entry)
 *
 *  Plug in your own:
 *      • candleFeed.subscribe()  → emits Candle (OHLCV + timestamp)
 *      • exchange.postOrder()    → async bracket / OCO order
 ********************************************************************/

// —————————————————— Types & Interfaces —————————————————— //
export type Timeframe = '5m' | '30m' | '1h';

export interface Candle {
  ts: number; // Unix-ms close time
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  tf: Timeframe;
}

enum OrderSide {
  LONG = 'LONG',
  SHORT = 'SHORT',
}

export interface OrderParams {
  side: OrderSide;
  qty: number;
  entry: number;
  stop: number;
  tp1: number;
  tp2: number;
  reduceOnly?: boolean;
}

export interface CandleFeed {
  /** fire callback on every closed candle of the requested timeframe */
  subscribe(tf: Timeframe, cb: (c: Candle) => void): void;
}

export interface Exchange {
  /** async OCO/bracket placement; implement for your broker */
  postOrder(o: OrderParams): Promise<void>;

  /** returns current position size (notional) for risk calc */
  getPosition(): Promise<number>;

  /** returns available account equity (USD) */
  getEquity(): Promise<number>;
}

// —————————————————— Utility Math —————————————————— //
class SMA {
  private vals: number[] = [];

  constructor(private len: number) {}

  push(v: number) {
    this.vals.push(v);
    if (this.vals.length > this.len) this.vals.shift();
  }

  value(): number | null {
    return this.vals.length === this.len ? this.vals.reduce((a, b) => a + b) / this.len : null;
  }
}

class EMA {
  private v: number | null = null;
  private readonly alpha: number;

  constructor(len: number) {
    this.alpha = 2 / (len + 1);
  }

  push(price: number) {
    this.v = this.v == null ? price : price * this.alpha + this.v * (1 - this.alpha);
  }

  value(): number | null {
    return this.v;
  }
}

class StdDev {
  private vals: number[] = [];

  constructor(private len: number) {}

  push(v: number) {
    this.vals.push(v);
    if (this.vals.length > this.len) this.vals.shift();
  }

  value(): number | null {
    if (this.vals.length < this.len) return null;
    const m = this.vals.reduce((a, b) => a + b) / this.len;
    const varSum = this.vals.reduce((s, x) => s + (x - m) ** 2, 0) / this.len;
    return Math.sqrt(varSum);
  }
}

// —————————————————— Strategy Bot —————————————————— //
export interface Config {
  riskPerTradePct: number; // 1% equity-risk on sleeve
  corePct: number; // 80 % long kept
  railExt: number; // 0.003 (0.3 %)
  volMult: number; // 1.3 × volSMA20
  minBBWidthPct: number; // 0.75 %
}

export class TradeSleeveBot {
  // indicators
  private ema9 = new EMA(9);
  private bbMidSMA = new SMA(20);
  private bbStd = new StdDev(20);
  private volSMA20 = new SMA(20);

  // HTF filter
  private htfEma21 = new EMA(21);
  private lastHtfClose: Candle | null = null;

  // state
  private inTrade = false;

  constructor(
    private cfg: Config,
    private feed: CandleFeed,
    private ex: Exchange,
  ) {
    this.feed.subscribe('1h', c => this.onHtf(c));
    this.feed.subscribe('5m', c => this.onLtf(c));
  }

  // ——— 1-Hour handler ———
  private onHtf(c: Candle) {
    this.htfEma21.push(c.close);
    this.lastHtfClose = c;
  }

  // ——— 5-Minute handler ———
  private async onLtf(c: Candle) {
    // update indicators
    this.ema9.push(c.close);
    this.bbMidSMA.push(c.close);
    this.bbStd.push(c.close);
    this.volSMA20.push(c.volume);

    if (this.inTrade) return; // already in sleeve trade

    // skip until indicators warm-up
    const mid = this.bbMidSMA.value();
    const sd = this.bbStd.value();
    const ema9 = this.ema9.value();
    const vol20 = this.volSMA20.value();
    const ema21htf = this.htfEma21.value();
    if (!mid || !sd || !ema9 || !vol20 || !ema21htf || !this.lastHtfClose) return;

    // ——— 0. Macro filter ———
    const htfBull =
      this.lastHtfClose.close > ema21htf && this.lastHtfClose.close > this.lastHtfClose.open; // rising candle proxy

    const bbUpper = mid + 2 * sd;
    const bbLower = mid - 2 * sd;
    const bbWidthPct = ((bbUpper - bbLower) / c.close) * 100;

    if (!htfBull || bbWidthPct < this.cfg.minBBWidthPct) return; // stand down

    // ——— 1. Look for entry signals ———
    const railLong =
      c.close > bbUpper &&
      c.close > ema9 * (1 + this.cfg.railExt) &&
      c.volume > vol20 * this.cfg.volMult;

    const dipToEMA = c.low <= ema9 && c.close > ema9;
    const dipToMid = c.low <= mid && c.close > mid;
    const dipLong = dipToEMA || dipToMid;

    if (!(railLong || dipLong)) return;

    // ——— 2. Risk & position sizing ———
    const stop = railLong ? ema9 * 0.999 : Math.min(c.low, dipToMid ? mid : ema9) * 0.999;
    const R = c.close - stop;
    if (R <= 0) return; // safety

    const equity = await this.ex.getEquity();
    const maxLoss = (equity * this.cfg.riskPerTradePct) / 100;
    const qty = maxLoss / R; // raw qty in coin-units (assuming coinUSDC pair)

    const tp1 = c.close + R;
    const tp2 = c.close + 2 * R;

    // ——— 3. Send bracket order ———
    const o: OrderParams = {
      side: OrderSide.LONG,
      qty,
      entry: c.close,
      stop,
      tp1,
      tp2,
    };
    await this.ex.postOrder(o);
    console.log(
      `[${new Date(c.ts).toISOString()}] Sleeve entry @${c.close} qty=${qty.toFixed(4)} stop=${stop} tp1=${tp1} tp2=${tp2}`,
    );
    this.inTrade = true;

    // naive: reset flag after TP2/SL via webhooks or polling; omitted here
  }
}
