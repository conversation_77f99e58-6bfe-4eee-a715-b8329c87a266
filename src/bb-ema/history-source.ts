import { Candle, CandleFeed, Exchange, OrderParams, Timeframe } from './bot';
import { HistoryProvider } from './history-provider';

export class HistoryFeed implements CandleFeed {
  private historyProvider: HistoryProvider;

  constructor(historyProvider: HistoryProvider) {
    this.historyProvider = historyProvider;
  }

  async subscribe(tf: Timeframe, cb: (c: Candle) => void) {
    this.historyProvider.subscribe(tf, candle => {
      cb({
        open: Number(candle.o),
        high: Number(candle.h),
        low: Number(candle.l),
        close: Number(candle.c),
        volume: Number(candle.v),
        ts: Number(candle.T),
        tf: tf,
      });
    });
  }
}

export class HistoryExchange implements Exchange {
  private historyProvider: HistoryProvider;

  constructor(historyProvider: HistoryProvider) {
    this.historyProvider = historyProvider;
  }

  async postOrder(o: OrderParams) {
    this.historyProvider.positionManager.openPosition(o.qty, o.entry, o.tp1, o.stop);
    // 1) open market/limit long
    // 2) attach TP1 / TP2 as reduce-only limits
    // 3) attach stop-loss
    // All via broker REST/WS API
  }
  async getPosition() {
    /* … */ return 0;
  }
  getEquity() {
    return 5_000;
  }
}
