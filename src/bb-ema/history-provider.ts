import { Timeframe } from './bot';
import { Candle as <PERSON><PERSON><PERSON><PERSON> } from '@nktkas/hyperliquid';
import * as fs from 'node:fs';
import { PositionManager } from './position-manager';
import { COIN } from './constants';

export class HistoryProvider {
  private candles: HLCandle[] = [];
  private callbacks = new Map<string, (c: HLCandle) => void>();
  private time = 0;
  private _positionManager?: PositionManager;

  subscribe(tf: Timeframe, cb: (c: HLCandle) => void) {
    const text = fs.readFileSync(`candles_${tf}.json`, 'utf8');
    const json = JSON.parse(text);
    this.candles.push(...json);
    this.callbacks.set(tf, cb);
  }

  public onClose?: () => void;

  get positionManager() {
    if (!this._positionManager) {
      this._positionManager = new PositionManager({
        token: COIN,
        currentPrice: Number(this.candles[0].c),
        fee: 0.00045,
        timeProvider: () => this.time,
        onClose: this.onClose,
      });
    }
    return this._positionManager;
  }

  play() {
    this.candles.sort((a, b) => a.T - b.T);
    while (this.candles.length > 0) {
      const next = this.candles[0];
      this.time = next.T;
      if (next.i === '5m') {
        this.positionManager.updatePrice(Number(next.c), Number(next.h), Number(next.l));
      }
      this.callbacks.get(next.i)?.(next);
      this.candles.shift();
    }
  }
}
