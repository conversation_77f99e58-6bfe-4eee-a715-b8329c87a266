interface PositionInfo {
  id: number;
  tokenAmount: number;
  entryPrice: number;
  takeProfit: number;
  stopLoss: number;
  openTime: number;
}

interface ClosedPosition {
  id: number;
  openTime: number;
  closeTime: number;
  side: 'long' | 'short';
  entryPrice: number;
  exitPrice: number;
  size: number;
  totalFees: number;
  pnl: number;
  duration: number;
}

interface PositionManagerConfig {
  token: string;
  currentPrice: number;
  fee: number;
  timeProvider?: () => number;
  onClose?: (position: ClosedPosition) => void;
}

export class PositionManager {
  private timeProvider: () => number;
  private positions = new Map<number, PositionInfo>();
  private config: PositionManagerConfig;
  private currentPrice: number;
  private allTimePnL = 0;
  private id = 1;
  private startedAt = 0;
  private report: ClosedPosition[] = [];

  constructor(config: PositionManagerConfig) {
    this.config = config;
    this.currentPrice = config.currentPrice;
    this.timeProvider = config.timeProvider || (() => Date.now());
  }

  public trackStart() {
    this.startedAt = this.timeProvider();
  }

  public openPosition(
    tokenAmount: number,
    entryPrice: number,
    takeProfit: number,
    stopLoss: number,
  ): number {
    const id = this.id++;
    entryPrice = this.currentPrice;
    this.positions.set(id, {
      id,
      tokenAmount,
      entryPrice,
      takeProfit,
      stopLoss,
      openTime: this.timeProvider(),
    });
    log(
      `Open ${tokenAmount.toFixed(4)} ${this.config.token} at $${entryPrice.toFixed(4)} (Total USD: $${(
        tokenAmount * entryPrice
      ).toFixed(2)}), TP: $${takeProfit.toFixed(4)}, SL: $${stopLoss.toFixed(4)}`,
    );
    return id;
  }

  public closePosition(positionId: number, price: number) {
    const position = this.positions.get(positionId);
    if (!position) {
      return;
    }

    this.positions.delete(positionId);
    const openFee = Math.abs(position.tokenAmount * position.entryPrice * this.config.fee);
    const closeFee = Math.abs(position.tokenAmount * price * this.config.fee);
    const totalFees = openFee + closeFee;
    const pnl = position.tokenAmount * (price - position.entryPrice) - totalFees;
    this.allTimePnL += pnl;
    log(
      `Close ${position.tokenAmount.toFixed(4)} ${this.config.token} at $${price.toFixed(4)}, PnL: $${pnl.toFixed(2)}, Fees: $${totalFees.toFixed(2)}, All time PnL: $${this.allTimePnL.toFixed(2)}`,
    );

    const closedPosition: ClosedPosition = {
      id: position.id,
      openTime: position.openTime,
      closeTime: this.timeProvider(),
      side: position.tokenAmount > 0 ? 'long' : 'short',
      entryPrice: position.entryPrice,
      exitPrice: price,
      size: position.tokenAmount,
      totalFees,
      pnl,
      duration: this.timeProvider() - position.openTime,
    };
    this.report.push(closedPosition);
    if (this.config.onClose) {
      this.config.onClose(closedPosition);
    }
    this.positions.delete(positionId);
  }

  public updatePrice(close: number, high: number, low: number) {
    this.currentPrice = close;
    for (const position of this.positions.values()) {
      if (position.tokenAmount > 0) {
        if (low <= position.stopLoss) {
          console.log('Stop loss hit');
          this.closePosition(position.id, position.stopLoss);
        } else if (high >= position.takeProfit) {
          console.log('Take profit hit');
          this.closePosition(position.id, position.takeProfit);
        }
      } else {
        if (high >= position.stopLoss) {
          console.log('Stop loss hit');
          this.closePosition(position.id, position.stopLoss);
        } else if (low <= position.takeProfit) {
          console.log('Take profit hit');
          this.closePosition(position.id, low);
        }
      }
    }
  }

  public printReport() {
    console.log('------');
    console.log('Report');
    console.log('------');

    console.log(`Fee: ${(this.config.fee * 100).toFixed(4)}%`);
    console.log(
      `Total gross PnL: $${this.report.reduce((sum, x) => sum + x.pnl + x.totalFees, 0).toFixed(2)}`,
    );
    console.log(`Total fees: $${this.report.reduce((sum, x) => sum + x.totalFees, 0).toFixed(2)}`);
    console.log(`Total net PnL: $${this.report.reduce((sum, x) => sum + x.pnl, 0).toFixed(2)}`);
    console.log(`Number of trades: ${this.report.length}`);
    console.log(
      `Win rate: ${((this.report.filter(x => x.pnl > 0).length / this.report.length) * 100).toFixed(2)}%`,
    );
    console.log(
      `Average Win (USD): $${(this.report.filter(x => x.pnl > 0).reduce((sum, x) => sum + x.pnl, 0) / this.report.filter(x => x.pnl > 0).length).toFixed(2)}`,
    );
    console.log(
      `Average Loss (USD): $${(this.report.filter(x => x.pnl < 0).reduce((sum, x) => sum + x.pnl, 0) / this.report.filter(x => x.pnl < 0).length).toFixed(2)}`,
    );
    console.log(`Max Drawdown (USD): $${maxDrawdown(this.report.map(x => x.pnl)).toFixed(2)}`);
    console.log(
      `Average Duration (ms): ${(this.report.reduce((sum, x) => sum + x.duration, 0) / this.report.length).toFixed(0)}`,
    );
    console.log(
      `Median Duration (ms): ${this.report
        .map(x => x.duration)
        .sort((a, b) => a - b)
        [Math.floor(this.report.length / 2)].toFixed(0)}`,
    );

    const durationPercentile = (percentile: number) => {
      return (
        this.report.map(x => x.duration).sort((a, b) => a - b)[
          Math.floor(this.report.length * percentile)
        ] / 1000
      );
    };
    console.log(`Duration percentile 25%: ${durationPercentile(0.25).toFixed(1)}s`);
    console.log(`Duration percentile 50%: ${durationPercentile(0.5).toFixed(1)}s`);
    console.log(`Duration percentile 75%: ${durationPercentile(0.75).toFixed(1)}s`);
    console.log(`Duration percentile 90%: ${durationPercentile(0.9).toFixed(1)}s`);
    console.log(`Duration percentile 95%: ${durationPercentile(0.9).toFixed(1)}s`);

    console.log(
      `% of trades longer than 60s: ${((this.report.filter(x => x.duration > 60000).length / this.report.length) * 100).toFixed(2)}%`,
    );
    console.log(
      `% of trades longer than 120s: ${((this.report.filter(x => x.duration > 120000).length / this.report.length) * 100).toFixed(2)}%`,
    );

    console.log(
      `Ran for ${((this.timeProvider() - this.startedAt) / 1000 / 60).toFixed(2)} minutes`,
    );
  }
}

function maxDrawdown(pnl: number[]): number {
  if (pnl.length === 0) return 0;

  let equity = 0;
  let maxEquity = 0;
  let maxDrawdown = 0; // This will be negative

  for (const profit of pnl) {
    equity += profit;
    if (equity > maxEquity) maxEquity = equity;
    const drawdown = equity - maxEquity; // This will be <= 0
    if (drawdown < maxDrawdown) maxDrawdown = drawdown;
  }
  return maxDrawdown; // This will be <= 0, like in pandas
}

function log(message: string) {
  console.log(message);
}
