import * as hl from '@nktkas/hyperliquid';
import { ethers } from 'ethers';

export const walletInfo = {
  address: '******************************************',
  privateKey: '0xcd13e738b2e9c16714eac33a77be6aa7ca3a91069a5d616dfa463cb935a074d6',
};

const wallet = new ethers.Wallet(walletInfo.privateKey);

export const transport = new hl.WebSocketTransport();
export const infoClient = new hl.InfoClient({ transport });
export const subsClient = new hl.SubscriptionClient({ transport });
export const exchangeClient = new hl.ExchangeClient({ wallet, transport });
