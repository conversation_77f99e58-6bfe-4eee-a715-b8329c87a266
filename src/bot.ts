/*
  Event-driven order book + trade-driven trading bot with timestamped multi-sig-fig views
  - Not exchange-agnostic; plug into any MarketInterface
  - Subscribes per significant-figure precision
  - Processes data on arrival to avoid polling
  - Smooths signals over a time window and computes weighted imbalance
  - Handles single-trade callbacks, maintaining recent trade history
  - Supports both long and short positions via signed token amounts
  - Incorporates trade-flow confirmation, execution cooldown, data freshness checks, and max-hold exits
  - Uses micro-price for entry and exit to reduce adverse selection
  - Allows injection of a custom time provider via config for testing/backtests
*/

// ----- Data Interfaces -----

export interface OrderBookEntry {
  price: number;
  size: number;
}

export interface OrderBookView {
  sigfig: number;
  timestamp: number; // Unix ms, as provided by feed
  bids: OrderBookEntry[];
  asks: OrderBookEntry[];
}

export interface TradeEntry {
  price: number;
  size: number;
  side: 'buy' | 'sell';
  timestamp: number; // Unix ms, as provided by feed
}

// ----- Market Interface -----

export interface MarketInterface {
  subscribeToOrderBook(sigfig: number, callback: (view: OrderBookView) => void): void;
  subscribeToTrades(callback: (trade: TradeEntry) => void): void;
  openPosition(
    tokenAmount: number,
    entryPrice: number,
    takeProfit: number,
    stopLoss: number,
  ): number;
  closePosition(positionId: number): void;
  onSignal(signal: 'long' | 'short' | 'none', price: number): void;
  symbol: string;
}

// ----- Bot Configuration -----

export interface BotConfig {
  accountValueUSD: number;
  currentPrice: number;
  riskPerTradePct: number;
  imbalanceThreshold: number;
  profitTargetPct: number;
  stopLossPct: number;
  lookbackSeconds: number;
  maxAgeMs: number; // freshness threshold for order book views
  cooldownMs: number; // min time between trades per symbol
  tradeImbalanceThreshold: number;
  maxHoldSeconds: number; // max holding time per trade
  timeProvider?: () => number; // optional override for Date.now()
}

// ----- Core Bot -----

export class TradingBot {
  private config: BotConfig;
  private markets: MarketInterface[];
  private nowFn: () => number;

  private bookHistory = new Map<number, OrderBookView[]>();
  private tradeHistory: TradeEntry[] = [];
  private lastTradeTime = 0;

  private sigfigs: number[];
  private weights: Record<number, number>;

  constructor(config: BotConfig, markets: MarketInterface[]) {
    this.config = config;
    this.markets = markets;
    this.nowFn = config.timeProvider || (() => Date.now());

    this.sigfigs = [2, 3, 4];
    const mag = Math.floor(Math.log10(config.currentPrice)) + 1;
    const raw: Record<number, number> = {};
    let sum = 0;
    for (const s of this.sigfigs) {
      const step = Math.pow(10, mag - s);
      raw[s] = 1 / step;
      sum += raw[s];
    }
    this.weights = {};
    for (const s of this.sigfigs) {
      this.weights[s] = raw[s] / sum;
    }
  }

  public start(): void {
    for (const market of this.markets) {
      for (const s of this.sigfigs) {
        market.subscribeToOrderBook(s, view => this.onBookView(market, s, view));
      }
      market.subscribeToTrades(trade => this.onTrade(trade));
    }
  }

  private onBookView(market: MarketInterface, sigfig: number, view: OrderBookView): void {
    const hist = this.bookHistory;
    const buf = hist.get(sigfig) || [];
    buf.push(view);
    const cutoffTime = this.nowFn() - this.config.lookbackSeconds * 1000;
    hist.set(
      sigfig,
      buf.filter(v => v.timestamp >= cutoffTime),
    );

    const now = this.nowFn();
    const allFresh = this.sigfigs.every(sf => {
      const arr = hist.get(sf) || [];
      const latest = arr[arr.length - 1];
      return latest !== undefined && now - latest.timestamp <= this.config.maxAgeMs;
    });
    if (allFresh) this.processMarket(market);
  }

  private onTrade(trade: TradeEntry): void {
    const buf = this.tradeHistory;
    buf.push(trade);
    const cutoffTime = this.nowFn() - this.config.lookbackSeconds * 1000;
    this.tradeHistory = buf.filter(t => t.timestamp >= cutoffTime);
  }

  private processMarket(market: MarketInterface): void {
    const now = this.nowFn();
    const hist = this.bookHistory;

    let totalImb = 0;
    for (const s of this.sigfigs) {
      const arr = hist.get(s)!;
      const recent = arr.filter(v => v.timestamp >= now - this.config.lookbackSeconds * 1000);
      const avgImb =
        recent.reduce((acc, v) => {
          const bidVol = v.bids.reduce((a, l) => a + l.size, 0);
          const askVol = v.asks.reduce((a, l) => a + l.size, 0);
          if (bidVol + askVol === 0) return acc;
          return acc + (bidVol - askVol) / (bidVol + askVol);
        }, 0) / (recent.length || 1);
      totalImb += avgImb * this.weights[s];
    }

    const trades = this.tradeHistory;
    const tradeVol = trades.reduce(
      (acc, t) => {
        acc.buy += t.side === 'buy' ? t.size : 0;
        acc.sell += t.side === 'sell' ? t.size : 0;
        return acc;
      },
      { buy: 0, sell: 0 },
    );
    const denom = tradeVol.buy + tradeVol.sell;
    const tapeImb = denom > 0 ? (tradeVol.buy - tradeVol.sell) / denom : 0;
    if (
      Math.sign(tapeImb) !== Math.sign(totalImb) ||
      Math.abs(tapeImb) < this.config.tradeImbalanceThreshold
    )
      return;

    const signal = this.generateSignal(totalImb);
    const highest = Math.max(...this.sigfigs);
    const latest = hist.get(highest)![hist.get(highest)!.length - 1];
    if (latest) this.executeSignal(market, signal, latest);
  }

  private generateSignal(avgImb: number): 'long' | 'short' | 'none' {
    if (avgImb > this.config.imbalanceThreshold) return 'long';
    if (avgImb < -this.config.imbalanceThreshold) return 'short';
    return 'none';
  }

  /** Compute micro-price: weighted fair value of top-of-book */
  private computeMicroPrice(bids: OrderBookEntry[], asks: OrderBookEntry[]): number {
    const bidVol = bids[0]?.size ?? 0;
    const askVol = asks[0]?.size ?? 0;
    const bidPrice = bids[0]?.price ?? 0;
    const askPrice = asks[0]?.price ?? 0;
    const total = bidVol + askVol;
    if (total === 0) return (bidPrice + askPrice) / 2;
    return (bidVol * askPrice + askVol * bidPrice) / total;
  }

  private executeSignal(
    market: MarketInterface,
    signal: 'long' | 'short' | 'none',
    view: OrderBookView,
  ): void {
    const now = this.nowFn();
    const last = this.lastTradeTime || 0;
    if (now - last < this.config.cooldownMs) return;

    const mid = (view.bids[0].price + view.asks[0].price) / 2;
    // use micro-price instead of midpoint
    const micro = this.computeMicroPrice(view.bids, view.asks);
    market.onSignal(signal, mid);
  }
}
