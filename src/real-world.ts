import { OrderBookView, TradeEntry } from './bot';
import { World } from './world';
import { infoClient, subsClient } from './hyperliquid';
import { MakerManager } from './maker-manager';
import { HyperManager } from './hyper-manager';

export const realWorld: World = {
  fetchPrice: async (coin: string) => {
    const data = await infoClient.allMids();
    return Number(data[coin]);
  },
  createMarketInterface: (coin: string, price: number) => {
    const makerManager = new MakerManager({
      token: coin,
      currentPrice: price,
      positionSizeUsd: 100,
      fee: 0.00015,
      planOffset: 0.0025,
      takeProfitPct: 0.05,
      stopLossPct: 0.05,
    });
    const hyperManager = new HyperManager({
      coin,
      assetId: 159,
      positionSizeUsd: 100,
      planOffset: 0.0025,
    });
    void hyperManager.start();
    return {
      symbol: coin,
      subscribeToOrderBook: (sigfig, callback) => {
        void subsClient.l2Book({ coin, nSigFigs: sigfig as 2 | 3 | 4 }, data => {
          const levels = data.levels;
          const bids = levels[0];
          const asks = levels[1];
          const orderBook: OrderBookView = {
            sigfig,
            timestamp: data.time,
            bids: bids.map((b: any) => ({
              price: Number(b.px),
              size: Number(b.sz),
            })),
            asks: asks.map((a: any) => ({
              price: Number(a.px),
              size: Number(a.sz),
            })),
          };
          callback(orderBook);
        });
      },
      subscribeToTrades: callback => {
        void subsClient.trades({ coin }, data => {
          for (const trade of data) {
            let entry: TradeEntry = {
              price: Number(trade.px),
              size: Number(trade.sz),
              side: trade.side === 'B' ? 'buy' : 'sell',
              timestamp: trade.time,
            };
            makerManager.updatePrice(Number(trade.px));
            callback(entry);
          }
        });
      },
      openPosition: (tokenAmount, entryPrice, takeProfit, stopLoss) => {
        return 0;
      },
      closePosition: id => {},
      onSignal: (signal, price) => {
        makerManager.onSignal(signal, price);
        void hyperManager.onSignal(signal, price);
      },
    };
  },
  getTime(): number {
    return Date.now();
  },
  start() {
    // Do nothing.
  },
};
