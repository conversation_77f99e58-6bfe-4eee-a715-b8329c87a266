import { exchangeClient, infoClient, subsClient, walletInfo } from './hyperliquid';
import { OrderParams } from '@nktkas/hyperliquid/esm/src/types/exchange/requests';
import { OrderResponseSuccess } from '@nktkas/hyperliquid/esm/src/clients/exchange';
import { Hex } from '@nktkas/hyperliquid';

interface SignalPlan {
  side: 'long' | 'short';
  entryPrice: number;
  orderId: Hex;
  order: OrderParams;
}

interface PositionInfo {
  side: 'long' | 'short';
  tokenAmount: number;
}

interface PositionManagerConfig {
  coin: string;
  assetId: number;
  positionSizeUsd: number;
  planOffset: number;
}

export class HyperManager {
  private plan: SignalPlan | undefined;
  private position: PositionInfo | undefined;
  private config: PositionManagerConfig;
  private isBusy = false;

  constructor(config: PositionManagerConfig) {
    this.config = config;
  }

  async start() {
    await subsClient.orderUpdates(
      {
        user: walletInfo.address as Hex,
      },
      async data => {
        for (const order of data) {
          if (this.plan && order.order.cloid === this.plan?.orderId && order.status === 'filled') {
            console.log(`Order ${order.order.oid} filled`);
            this.isBusy = true;
            this.position = {
              side: this.plan.side,
              tokenAmount: await this.fetchTokenCount(),
            };
            this.plan = undefined;
            this.isBusy = false;
          }
        }
      },
    );
  }

  async fetchTokenCount() {
    const state = await infoClient.clearinghouseState({
      user: '******************************************',
    });
    const count = Number(
      state.assetPositions.find(x => x.position.coin === this.config.coin)?.position.szi ?? 0,
    );
    console.log('Token count: ' + count);
    return count;
  }

  async onSignal(signal: 'long' | 'short' | 'none', price: number) {
    if (this.isBusy) {
      return;
    }
    this.isBusy = true;
    await this.processSignal(signal, price);
    this.isBusy = false;
  }

  async processSignal(signal: 'long' | 'short' | 'none', price: number) {
    if (signal === 'none') {
      return;
    }

    if (this.position && this.position.side === signal) {
      if (this.plan) {
        console.log(`Canceling opposite order ${this.plan.orderId} as position follows signal`);
        await exchangeClient.cancelByCloid({
          cancels: [
            {
              asset: this.config.assetId,
              cloid: this.plan.orderId,
            },
          ],
        });
        this.plan = undefined;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      return;
    }

    const entryPrice =
      signal === 'long'
        ? price * (1 - this.config.planOffset)
        : price * (1 + this.config.planOffset);

    if (this.plan) {
      if (signal === this.plan.side) {
        if (signal === 'long' && entryPrice <= this.plan.entryPrice) {
          return;
        }
        if (signal === 'short' && entryPrice >= this.plan.entryPrice) {
          return;
        }
        const changeTolerance = 0.0001;
        if (Math.abs(entryPrice - this.plan.entryPrice) / this.plan.entryPrice < changeTolerance) {
          return;
        }
      }
      const positionSize =
        this.config.positionSizeUsd / entryPrice + Math.abs(this.position?.tokenAmount ?? 0);
      console.log(price);
      console.log(`Modifying order ${this.plan.orderId} to ${positionSize} @ ${entryPrice}`);
      this.plan.entryPrice = entryPrice;
      this.plan.order = {
        ...this.plan.order,
        b: signal === 'long',
        p: entryPrice.toFixed(3),
        s: positionSize.toFixed(2),
      };
      await exchangeClient.modify({
        oid: this.plan.orderId as any,
        order: this.plan.order,
      });
      await new Promise(resolve => setTimeout(resolve, 1000));
      return;
    }

    const positionSize =
      this.config.positionSizeUsd / entryPrice + Math.abs(this.position?.tokenAmount ?? 0);

    const cloid = ('0x' +
      Array.from(crypto.getRandomValues(new Uint8Array(16)))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')) as Hex;
    const order: OrderParams = {
      a: this.config.assetId,
      b: signal === 'long',
      c: cloid,
      p: entryPrice.toFixed(3),
      s: positionSize.toFixed(2),
      r: false,
      t: {
        limit: {
          tif: 'Gtc',
        },
      },
    };
    console.log(price);
    console.log(`Placing ${signal} order ${order.s} @ ${order.p} (${cloid})`);
    const response = await exchangeClient.order({
      orders: [order],
      grouping: 'na',
    });
    let result = getOrderResult(response);
    if (result.resting) {
      console.log(`Order ${cloid} is resting`);
      this.plan = {
        side: signal,
        entryPrice,
        orderId: cloid,
        order,
      };
    } else {
      console.log(`Order ${cloid} is filled`);
      this.position = {
        side: signal,
        tokenAmount: await this.fetchTokenCount(),
      };
    }
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

function getOrderResult(response: OrderResponseSuccess) {
  const status = response.response.data.statuses[0] as any;
  return {
    oid: status.resting?.oid ?? status.filled?.oid,
    resting: status.resting,
  };
}
