interface SignalPlan {
  side: 'long' | 'short';
  price: number;
}

interface PositionInfo {
  side: 'long' | 'short';
  tokenAmount: number;
  entryPrice: number;
  takeProfit: number;
  stopLoss: number;
  openTime: number;
}

interface ClosedPosition {
  openTime: number;
  closeTime: number;
  entryPrice: number;
  exitPrice: number;
  size: number;
  totalFees: number;
  pnl: number;
  duration: number;
}

interface PositionManagerConfig {
  token: string;
  currentPrice: number;
  positionSizeUsd: number;
  planOffset: number;
  fee: number;
  takeProfitPct: number;
  stopLossPct: number;
  timeProvider?: () => number;
}

export class MakerManager {
  private timeProvider: () => number;
  private plan: SignalPlan | undefined;
  private position: PositionInfo | undefined;
  private token: string;
  private fee: number;
  private currentPrice: number;
  private allTimePnL = 0;
  private startedAt = 0;
  private positionSize: number;
  private planOffset: number;
  private takeProfitPct: number;
  private stopLossPct: number;
  private report: ClosedPosition[] = [];

  constructor(config: PositionManagerConfig) {
    this.token = config.token;
    this.currentPrice = config.currentPrice;
    this.fee = config.fee;
    this.positionSize = config.positionSizeUsd;
    this.planOffset = config.planOffset;
    this.takeProfitPct = config.takeProfitPct;
    this.stopLossPct = config.stopLossPct;
    this.timeProvider = config.timeProvider || (() => Date.now());
  }

  public trackStart() {
    this.startedAt = this.timeProvider();
  }

  public onSignal(signal: 'long' | 'short' | 'none', price: number) {
    if (signal === 'none') {
      return;
    }

    if (this.position && this.position.side === signal) {
      this.plan = undefined;
      return;
    }

    if (this.plan) {
      this.plan.price =
        signal === 'long' ? Math.min(this.plan.price, price) : Math.max(this.plan.price, price);
    } else {
      this.plan = { side: signal, price };
    }
  }

  public updatePrice(price: number) {
    this.currentPrice = price;
    const plan = this.plan;
    if (!plan) {
      return;
    }

    if (plan.side === 'long') {
      if (price >= plan.price * (1 + this.planOffset)) {
        this.openPosition(plan);
        this.plan = undefined;
      }
      if (price <= plan.price * (1 - this.planOffset)) {
        this.plan = undefined;
      }
    }

    if (plan.side === 'short') {
      if (price <= plan.price * (1 - this.planOffset)) {
        this.openPosition(plan);
        this.plan = undefined;
      }
      if (price >= plan.price * (1 + this.planOffset)) {
        this.plan = undefined;
      }
    }

    if (this.position) {
      if (this.position.side === 'long') {
        if (price >= this.position.takeProfit) {
          this.closePosition();
        } else if (price <= this.position.stopLoss) {
          this.closePosition();
        }
      } else {
        if (price <= this.position.takeProfit) {
          this.closePosition();
        } else if (price >= this.position.stopLoss) {
          this.closePosition();
        }
      }
    }
  }

  private openPosition(plan: SignalPlan) {
    this.closePosition();
    let entryPrice = this.currentPrice;
    let tokenAmount = this.positionSize / entryPrice;
    this.position = {
      side: plan.side,
      tokenAmount: plan.side === 'long' ? tokenAmount : -tokenAmount,
      entryPrice,
      takeProfit:
        plan.side === 'long'
          ? entryPrice * (1 + this.takeProfitPct)
          : entryPrice * (1 - this.takeProfitPct),
      stopLoss:
        plan.side === 'long'
          ? entryPrice * (1 - this.stopLossPct)
          : entryPrice * (1 + this.stopLossPct),
      openTime: this.timeProvider(),
    };
    log(
      `Open ${this.position.tokenAmount.toFixed(4)} ${this.token} at $${entryPrice.toFixed(4)} (Total USD: $${(
        tokenAmount * entryPrice
      ).toFixed(2)})`,
    );
  }

  private closePosition() {
    const position = this.position;
    if (!position) {
      return;
    }

    const openFee = Math.abs(position.tokenAmount * position.entryPrice * this.fee);
    const closeFee = Math.abs(position.tokenAmount * this.currentPrice * this.fee);
    const totalFees = openFee + closeFee;
    const pnl = position.tokenAmount * (this.currentPrice - position.entryPrice) - totalFees;
    this.allTimePnL += pnl;
    log(
      `Close ${position.tokenAmount.toFixed(4)} ${this.token} at $${this.currentPrice.toFixed(4)}, PnL: $${pnl.toFixed(2)}, Fees: $${totalFees.toFixed(2)}, All time PnL: $${this.allTimePnL.toFixed(2)}`,
    );

    const closedPosition: ClosedPosition = {
      openTime: position.openTime,
      closeTime: this.timeProvider(),
      entryPrice: position.entryPrice,
      exitPrice: this.currentPrice,
      size: position.tokenAmount,
      totalFees,
      pnl,
      duration: this.timeProvider() - position.openTime,
    };
    this.report.push(closedPosition);
    this.position = undefined;
  }

  public printReport() {
    console.log('------');
    console.log('Report');
    console.log('------');

    console.log(`Fee: ${(this.fee * 100).toFixed(4)}%`);
    console.log(
      `Total gross PnL: $${this.report.reduce((sum, x) => sum + x.pnl + x.totalFees, 0).toFixed(2)}`,
    );
    console.log(`Total fees: $${this.report.reduce((sum, x) => sum + x.totalFees, 0).toFixed(2)}`);
    console.log(`Total net PnL: $${this.report.reduce((sum, x) => sum + x.pnl, 0).toFixed(2)}`);
    console.log(`Number of trades: ${this.report.length}`);
    console.log(
      `Win rate: ${((this.report.filter(x => x.pnl > 0).length / this.report.length) * 100).toFixed(2)}%`,
    );
    console.log(
      `Average Win (USD): $${(this.report.filter(x => x.pnl > 0).reduce((sum, x) => sum + x.pnl, 0) / this.report.filter(x => x.pnl > 0).length).toFixed(2)}`,
    );
    console.log(
      `Average Loss (USD): $${(this.report.filter(x => x.pnl < 0).reduce((sum, x) => sum + x.pnl, 0) / this.report.filter(x => x.pnl < 0).length).toFixed(2)}`,
    );
    console.log(`Max Drawdown (USD): $${maxDrawdown(this.report.map(x => x.pnl)).toFixed(2)}`);
    console.log(
      `Average Duration (ms): ${(this.report.reduce((sum, x) => sum + x.duration, 0) / this.report.length).toFixed(0)}`,
    );
    console.log(
      `Median Duration (ms): ${this.report
        .map(x => x.duration)
        .sort((a, b) => a - b)
        [Math.floor(this.report.length / 2)].toFixed(0)}`,
    );

    const durationPercentile = (percentile: number) => {
      return (
        this.report.map(x => x.duration).sort((a, b) => a - b)[
          Math.floor(this.report.length * percentile)
        ] / 1000
      );
    };
    console.log(`Duration percentile 25%: ${durationPercentile(0.25).toFixed(1)}s`);
    console.log(`Duration percentile 50%: ${durationPercentile(0.5).toFixed(1)}s`);
    console.log(`Duration percentile 75%: ${durationPercentile(0.75).toFixed(1)}s`);
    console.log(`Duration percentile 90%: ${durationPercentile(0.9).toFixed(1)}s`);
    console.log(`Duration percentile 95%: ${durationPercentile(0.9).toFixed(1)}s`);

    console.log(
      `% of trades longer than 60s: ${((this.report.filter(x => x.duration > 60000).length / this.report.length) * 100).toFixed(2)}%`,
    );
    console.log(
      `% of trades longer than 120s: ${((this.report.filter(x => x.duration > 120000).length / this.report.length) * 100).toFixed(2)}%`,
    );

    console.log(
      `Ran for ${((this.timeProvider() - this.startedAt) / 1000 / 60).toFixed(2)} minutes`,
    );
  }
}

function maxDrawdown(pnl: number[]): number {
  if (pnl.length === 0) return 0;

  let equity = 0;
  let maxEquity = 0;
  let maxDrawdown = 0; // This will be negative

  for (const profit of pnl) {
    equity += profit;
    if (equity > maxEquity) maxEquity = equity;
    const drawdown = equity - maxEquity; // This will be <= 0
    if (drawdown < maxDrawdown) maxDrawdown = drawdown;
  }
  return maxDrawdown; // This will be <= 0, like in pandas
}

function log(message: string) {
  console.log(message);
}
