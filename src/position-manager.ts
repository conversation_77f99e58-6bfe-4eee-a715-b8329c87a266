import * as fs from 'node:fs';

interface PositionInfo {
  id: number;
  entered: boolean;
  tokenAmount: number;
  entryPrice: number;
  takeProfit: number;
  stopLoss: number;
  openTime: number;
}

interface ClosedPosition {
  id: number;
  openTime: number;
  closeTime: number;
  side: 'long' | 'short';
  entryPrice: number;
  exitPrice: number;
  size: number;
  totalFees: number;
  pnl: number;
  duration: number;
}

interface PositionManagerConfig {
  token: string;
  currentPrice: number;
  fee: number;
  timeProvider?: () => number;
}

export class PositionManager {
  private timeProvider: () => number;
  private positions = new Map<number, PositionInfo>();
  private token: string;
  private fee: number;
  private currentPrice: number;
  private allTimePnL = 0;
  private id = 1;
  private startedAt = 0;
  private report: ClosedPosition[] = [];

  constructor(config: PositionManagerConfig) {
    this.token = config.token;
    this.currentPrice = config.currentPrice;
    this.fee = config.fee;
    this.timeProvider = config.timeProvider || (() => Date.now());
  }

  public trackStart() {
    this.startedAt = this.timeProvider();
  }

  public openPosition(
    tokenAmount: number,
    entryPrice: number,
    takeProfit: number,
    stopLoss: number,
  ): number {
    const id = this.id++;
    this.positions.set(id, {
      id,
      entered: false,
      tokenAmount,
      entryPrice,
      takeProfit,
      stopLoss,
      openTime: this.timeProvider(),
    });
    log(
      `Open ${tokenAmount.toFixed(4)} ${this.token} at $${entryPrice.toFixed(4)} (Total USD: $${(
        tokenAmount * entryPrice
      ).toFixed(2)}), TP: $${takeProfit.toFixed(4)}, SL: $${stopLoss.toFixed(4)}`,
    );
    return id;
  }

  public closePosition(positionId: number) {
    const position = this.positions.get(positionId);
    if (!position) {
      return;
    }

    this.positions.delete(positionId);

    if (!position.entered) {
      return;
    }

    const openFee = Math.abs(position.tokenAmount * position.entryPrice * this.fee);
    const closeFee = Math.abs(position.tokenAmount * this.currentPrice * this.fee);
    const totalFees = openFee + closeFee;
    const pnl = position.tokenAmount * (this.currentPrice - position.entryPrice) - totalFees;
    this.allTimePnL += pnl;
    log(
      `Close ${position.tokenAmount.toFixed(4)} ${this.token} at $${this.currentPrice.toFixed(4)}, PnL: $${pnl.toFixed(2)}, Fees: $${totalFees.toFixed(2)}, All time PnL: $${this.allTimePnL.toFixed(2)}`,
    );

    const closedPosition: ClosedPosition = {
      id: position.id,
      openTime: position.openTime,
      closeTime: this.timeProvider(),
      side: position.tokenAmount > 0 ? 'long' : 'short',
      entryPrice: position.entryPrice,
      exitPrice: this.currentPrice,
      size: position.tokenAmount,
      totalFees,
      pnl,
      duration: this.timeProvider() - position.openTime,
    };
    this.report.push(closedPosition);
    //logClosedPosition(closedPosition);
    this.positions.delete(positionId);
  }

  public updatePrice(price: number) {
    this.currentPrice = price;
    for (const position of this.positions.values()) {
      if (position.tokenAmount > 0) {
        if (!position.entered) {
          if (this.currentPrice >= position.entryPrice * 1.0025) {
            position.entered = true;
          }
          continue;
        }
        if (this.currentPrice <= position.stopLoss) {
          console.log('Stop loss hit');
          this.closePosition(position.id);
        } else if (this.currentPrice >= position.takeProfit) {
          console.log('Take profit hit');
          this.closePosition(position.id);
        }
      } else {
        if (!position.entered) {
          if (this.currentPrice <= position.entryPrice * 1.0025) {
            position.entered = true;
          }
          continue;
        }
        if (this.currentPrice >= position.stopLoss) {
          console.log('Stop loss hit');
          this.closePosition(position.id);
        } else if (this.currentPrice <= position.takeProfit) {
          console.log('Take profit hit');
          this.closePosition(position.id);
        }
      }
    }
  }

  public printReport() {
    console.log('------');
    console.log('Report');
    console.log('------');

    console.log(`Fee: ${(this.fee * 100).toFixed(4)}%`);
    console.log(
      `Total gross PnL: $${this.report.reduce((sum, x) => sum + x.pnl + x.totalFees, 0).toFixed(2)}`,
    );
    console.log(`Total fees: $${this.report.reduce((sum, x) => sum + x.totalFees, 0).toFixed(2)}`);
    console.log(`Total net PnL: $${this.report.reduce((sum, x) => sum + x.pnl, 0).toFixed(2)}`);
    console.log(`Number of trades: ${this.report.length}`);
    console.log(
      `Win rate: ${((this.report.filter(x => x.pnl > 0).length / this.report.length) * 100).toFixed(2)}%`,
    );
    console.log(
      `Average Win (USD): $${(this.report.filter(x => x.pnl > 0).reduce((sum, x) => sum + x.pnl, 0) / this.report.filter(x => x.pnl > 0).length).toFixed(2)}`,
    );
    console.log(
      `Average Loss (USD): $${(this.report.filter(x => x.pnl < 0).reduce((sum, x) => sum + x.pnl, 0) / this.report.filter(x => x.pnl < 0).length).toFixed(2)}`,
    );
    console.log(`Max Drawdown (USD): $${maxDrawdown(this.report.map(x => x.pnl)).toFixed(2)}`);
    console.log(
      `Average Duration (ms): ${(this.report.reduce((sum, x) => sum + x.duration, 0) / this.report.length).toFixed(0)}`,
    );
    console.log(
      `Median Duration (ms): ${this.report
        .map(x => x.duration)
        .sort((a, b) => a - b)
        [Math.floor(this.report.length / 2)].toFixed(0)}`,
    );

    const durationPercentile = (percentile: number) => {
      return (
        this.report.map(x => x.duration).sort((a, b) => a - b)[
          Math.floor(this.report.length * percentile)
        ] / 1000
      );
    };
    console.log(`Duration percentile 25%: ${durationPercentile(0.25).toFixed(1)}s`);
    console.log(`Duration percentile 50%: ${durationPercentile(0.5).toFixed(1)}s`);
    console.log(`Duration percentile 75%: ${durationPercentile(0.75).toFixed(1)}s`);
    console.log(`Duration percentile 90%: ${durationPercentile(0.9).toFixed(1)}s`);
    console.log(`Duration percentile 95%: ${durationPercentile(0.9).toFixed(1)}s`);

    console.log(
      `% of trades longer than 60s: ${((this.report.filter(x => x.duration > 60000).length / this.report.length) * 100).toFixed(2)}%`,
    );
    console.log(
      `% of trades longer than 120s: ${((this.report.filter(x => x.duration > 120000).length / this.report.length) * 100).toFixed(2)}%`,
    );

    console.log(
      `Ran for ${((this.timeProvider() - this.startedAt) / 1000 / 60).toFixed(2)} minutes`,
    );
  }
}

function maxDrawdown(pnl: number[]): number {
  if (pnl.length === 0) return 0;

  let equity = 0;
  let maxEquity = 0;
  let maxDrawdown = 0; // This will be negative

  for (const profit of pnl) {
    equity += profit;
    if (equity > maxEquity) maxEquity = equity;
    const drawdown = equity - maxEquity; // This will be <= 0
    if (drawdown < maxDrawdown) maxDrawdown = drawdown;
  }
  return maxDrawdown; // This will be <= 0, like in pandas
}

if (fs.existsSync('log.txt')) {
  fs.unlinkSync('log.txt');
}

function log(message: string) {
  console.log(message);
  //fs.appendFileSync('log.txt', message + '\n');
}

if (fs.existsSync('log.csv')) {
  fs.unlinkSync('log.csv');
}

function logCsv(message: string) {
  fs.appendFileSync('log.csv', message + '\n');
}

function logClosedPosition(position: ClosedPosition) {
  fs.appendFileSync(
    'log.csv',
    `${position.id},${position.openTime},${position.closeTime},${position.side},${position.entryPrice},${position.exitPrice},${position.size},${position.totalFees.toFixed(2)},${position.pnl.toFixed(2)},${position.duration}\n`,
  );
}

logCsv('trade_id,t_entry,t_exit,side,entry_price,exit_price,size,total_fees,pnl_usd,duration_ms');
