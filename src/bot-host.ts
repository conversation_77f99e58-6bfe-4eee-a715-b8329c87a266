import { BotConfig, MarketInterface, TradingBot } from './bot';
import { testWorld } from './test-world';
import { realWorld } from './real-world';

const coin = 'HYPE';
const test = false;
const world = test ? testWorld : realWorld;

async function run() {
  const price = await world.fetchPrice(coin);
  console.log(`Current price of ${coin}: ${price}`);

  const config: BotConfig = {
    accountValueUSD: 1_000, // total capital in USD
    currentPrice: price, // current token price
    riskPerTradePct: 0.005, // risk 0.5% of account per trade
    imbalanceThreshold: 0.1, // require >10% weighted book imbalance
    profitTargetPct: 0.0175, // take profit at +1.75%
    stopLossPct: 0.01, // stop loss at –1%
    lookbackSeconds: 5, // 5-second history window
    maxAgeMs: 1000, // views must be ≤1000 ms old
    cooldownMs: 500, // at least 500 ms between trades
    tradeImbalanceThreshold: 0.05, // require >5% tape-flow imbalance
    maxHoldSeconds: Number.POSITIVE_INFINITY,
    timeProvider: world.getTime,
  };
  const markets = [] as MarketInterface[];
  markets.push(world.createMarketInterface(coin, price));
  const bot = new TradingBot(config, markets);
  bot.start();
  if (world === testWorld) {
    world.start();
  }
}

run().catch(console.error);
