import { World } from './world';
import { OrderBookView, TradeEntry } from './bot';
import * as fs from 'node:fs';
import { MakerManager } from './maker-manager';

interface DataStream {
  nextTimeStamp: number | undefined;

  moveNext(): void;
}

const dataStreams = [] as DataStream[];
let time = 0;
let makerManager: MakerManager;

export const testWorld: World = {
  fetchPrice: async (coin: string) => {
    return 33.5395;
  },
  createMarketInterface: (coin: string, price: number) => {
    makerManager = new MakerManager({
      token: coin,
      currentPrice: price,
      positionSizeUsd: 500,
      fee: 0.00015,
      planOffset: 0.0025,
      takeProfitPct: 0.05,
      stopLossPct: 0.05,
      timeProvider: testWorld.getTime,
    });
    return {
      symbol: coin,
      subscribeToOrderBook: (sigfig, callback) => {
        subscribeL2Book(sigfig, data => {
          const levels = data.levels;
          const bids = levels[0];
          const asks = levels[1];
          const orderBook: OrderBookView = {
            sigfig,
            timestamp: data.time,
            bids: bids.map((b: any) => ({
              price: Number(b.px),
              size: Number(b.sz),
            })),
            asks: asks.map((a: any) => ({
              price: Number(a.px),
              size: Number(a.sz),
            })),
          };
          callback(orderBook);
        });
      },
      subscribeToTrades: callback => {
        subscribeTrades(data => {
          for (const trade of data) {
            let entry: TradeEntry = {
              price: Number(trade.px),
              size: Number(trade.sz),
              side: trade.side === 'B' ? 'buy' : 'sell',
              timestamp: trade.time,
            };
            makerManager.updatePrice(Number(trade.px));
            callback(entry);
          }
        });
      },
      openPosition: (tokenAmount, entryPrice, takeProfit, stopLoss) => {
        return 0;
      },
      closePosition: id => {},
      onSignal(signal: 'long' | 'short' | 'none', price: number) {
        //console.log('On signal: ' + signal + ' at ' + price);
        makerManager.onSignal(signal, price);
      },
    };
  },
  getTime: () => time,
  start() {
    while (true) {
      let minNextTimeStamp = Number.POSITIVE_INFINITY;
      let nextDataStream: DataStream | undefined = undefined;
      for (const stream of dataStreams) {
        if (stream.nextTimeStamp === undefined) {
          continue;
        }
        if (stream.nextTimeStamp < minNextTimeStamp) {
          minNextTimeStamp = stream.nextTimeStamp;
          nextDataStream = stream;
        }
      }
      if (nextDataStream === undefined) {
        break;
      }
      if (!time) {
        time = minNextTimeStamp;
        makerManager.trackStart();
      }
      time = minNextTimeStamp;
      nextDataStream.moveNext();
    }
    makerManager.printReport();
  },
};

function subscribeL2Book(sigfig: number, callback: (data: any) => void) {
  const lines = fs.readFileSync(`l2Book_${sigfig}.txt`, 'utf8').split('\n');
  let index = 0;
  let data: any;
  const stream: DataStream = {
    nextTimeStamp: undefined,
    moveNext() {
      if (data) {
        callback(data);
      }
      this.nextTimeStamp = undefined;
      if (index >= lines.length) {
        return;
      }
      const line = lines[index++];
      if (!line) {
        return;
      }
      data = JSON.parse(line);
      this.nextTimeStamp = data.time;
    },
  };
  stream.moveNext();
  dataStreams.push(stream);
}

function subscribeTrades(callback: (data: any) => void) {
  const lines = fs.readFileSync(`trades.txt`, 'utf8').split('\n');
  let index = 0;
  let data: any;
  const stream: DataStream = {
    nextTimeStamp: undefined,
    moveNext() {
      if (data) {
        callback(data);
      }
      this.nextTimeStamp = undefined;
      if (index >= lines.length) {
        return;
      }
      const line = lines[index++];
      if (!line) {
        return;
      }
      data = JSON.parse(line);
      this.nextTimeStamp = data[0].time;
    },
  };
  stream.moveNext();
  dataStreams.push(stream);
}
