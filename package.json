{"name": "lp-bot", "version": "1.0.0", "private": "true", "scripts": {"start": "tsx src/bb-ema/host.ts", "collect": "tsx src/bot-host.ts", "prettier": "prettier . --write --ignore-path .prettierignore"}, "dependencies": {"@ethersproject/abi": "^5.8.0", "@ethersproject/providers": "^5.8.0", "@nktkas/hyperliquid": "^0.21.1", "@pancakeswap/sdk": "^5.8.14", "@pancakeswap/smart-router": "^7.2.3", "@pancakeswap/swap-sdk-core": "^1.4.0", "@pancakeswap/tokens": "^0.7.4", "@pancakeswap/v3-core": "^1.0.0", "@pancakeswap/v3-sdk": "^3.9.0", "@types/node": "^22.15.20", "@types/ws": "^8.18.1", "dotenv": "^16.5.0", "ethers": "^5.7.2", "ethers-multicall": "^0.2.3", "ethers-multicall-provider": "^3.1.2", "graphql-request": "^5.0.0", "jsbi": "^3.2.5", "telegraf": "^4.16.3", "typescript": "^5.0.0", "viem": "^2.22.23", "ws": "^8.18.2"}, "devDependencies": {"prettier": "^3.3.2", "tsx": "^4.19.4"}}